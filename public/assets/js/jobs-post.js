(function() {
    "use strict"

    // for personal information language
    const languageElement = document.getElementById('language');
    if (languageElement) {
        const multipleCancelButton = new Choices(
            '#language',
            {
                allowHTML: true,
                removeItemButton: true,
            }
        );
    }

    // for personal information skills
    const skillsElement = document.getElementById('skills');
    if (skillsElement) {
        const multipleCancelButton2 = new Choices(
            '#skills',
            {
                allowHTML: true,
                removeItemButton: true,
            }
        );
    }

    // for personal information qualification
    const qualificationElement = document.getElementById('qualification');
    if (qualificationElement) {
        const multipleCancelButton3 = new Choices(
            '#qualification',
            {
                allowHTML: true,
                removeItemButton: true,
            }
        );
    }
    /* For Date Range Picker */
    flatpickr("#job-deadline", {
        mode: "range",
        dateFormat: "Y-m-d",
    });
     
})();