(function () {
  "use strict";

  /* default multi select */
  const choicesMultipleDefaultElement = document.getElementById('choices-multiple-default');
  if (choicesMultipleDefaultElement) {
    const secondElement = new Choices('#choices-multiple-default', { allowSearch: false }).setValue(['Choice 2', 'Choice 3']);
  }

  /* multi select with remove button */
  const choicesMultipleRemoveButtonElement = document.getElementById('choices-multiple-remove-button');
  if (choicesMultipleRemoveButtonElement) {
    const multipleCancelButton = new Choices(
      '#choices-multiple-remove-button',
      {
        allowHTML: true,
        removeItemButton: true,
      }
    );
  }

  /* multi select with option groups */
  const choicesMultipleGroupsElement = document.getElementById('choices-multiple-groups');
  if (choicesMultipleGroupsElement) {
    const multipleDefault = new Choices(
      choicesMultipleGroupsElement,
      { allowHTML: true }
    );
  }

  /* email address only */
  const choicesTextEmailFilterElement = document.getElementById('choices-text-email-filter');
  if (choicesTextEmailFilterElement) {
    var textEmailFilter = new Choices('#choices-text-email-filter', {
      allowHTML: true,
      editItems: true,
      addItemFilter: function (value) {
        if (!value) {
          return false;
        }
        const regex = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        const expression = new RegExp(regex.source, 'i');
        return expression.test(value);
      },
    }).setValue(['<EMAIL>']);
  }

  /* passing through values */
  const choicesTextPresetValuesElement = document.getElementById('choices-text-preset-values');
  if (choicesTextPresetValuesElement) {
    var textPresetVal = new Choices('#choices-text-preset-values', {
      allowHTML: true,
      items: [
        'one',
        {
          value: 'two',
          label: 'two',
          customProperties: {
            description: 'Numbers are infinite',
          },
        },
      ],
    });
  }

  /* options added via config with no search */
  const choicesSingleNoSearchElement = document.getElementById('choices-single-no-search');
  if (choicesSingleNoSearchElement) {
    var singleNoSearch = new Choices('#choices-single-no-search', {
      allowHTML: true,
      searchEnabled: false,
      removeItemButton: true,
      choices: [
        { value: 'One', label: 'Label One' },
        { value: 'Two', label: 'Label Two' },
        { value: 'Three', label: 'Label Three' },
      ],
    }).setChoices(
      [
        { value: 'Four', label: 'Label Four' },
        { value: 'Five', label: 'Label Five' },
        { value: 'Six', label: 'Label Six', selected: true },
      ],
      'value',
      'label',
      false
    );
  }

  /* passing unique values */
  const choicesTextUniqueValuesElement = document.getElementById('choices-text-unique-values');
  if (choicesTextUniqueValuesElement) {
    var textUniqueVals = new Choices('#choices-text-unique-values', {
      allowHTML: true,
      paste: false,
      duplicateItemsAllowed: false,
      editItems: true,
    });
  }

})();